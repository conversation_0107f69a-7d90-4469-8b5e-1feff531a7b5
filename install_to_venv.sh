#!/bin/bash

# Check if venv path is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <path_to_venv>"
    echo "Example: $0 /path/to/your/venv"
    echo "Example: $0 .venv"
    exit 1
fi

VENV_PATH="$1"

# Check if the provided venv path exists
if [ ! -d "$VENV_PATH" ]; then
    echo "Error: Virtual environment directory '$VENV_PATH' does not exist."
    echo "Please create the virtual environment first or provide a valid path."
    exit 1
fi

# Check if it's a valid virtual environment
if [ ! -f "$VENV_PATH/bin/activate" ] && [ ! -f "$VENV_PATH/Scripts/activate" ]; then
    echo "Error: '$VENV_PATH' does not appear to be a valid virtual environment."
    echo "Missing activation script (bin/activate or Scripts/activate)."
    exit 1
fi

# Determine the OS type
OS_TYPE=$(uname)
IS_WINDOWS=false
IS_WSL=false

if [[ "$OS_TYPE" == "Darwin" ]]; then
    echo "OS detected: macOS"
    PACKAGE_MANAGER="brew"
elif [[ "$OS_TYPE" == "Linux" ]]; then
    if grep -qi microsoft /proc/version; then
        echo "OS detected: Windows Subsystem for Linux (WSL)"
        IS_WSL=true
    else
        echo "OS detected: Linux"
    fi
    PACKAGE_MANAGER="apt"
else
    echo "OS detected: Windows (assuming Git Bash or WSL)"
    IS_WINDOWS=true
    PACKAGE_MANAGER="choco"
fi

echo "Using virtual environment: $VENV_PATH"

echo "Activating environment..."
if [[ "$IS_WINDOWS" == true ]]; then
    source "$VENV_PATH/Scripts/activate"
else
    source "$VENV_PATH/bin/activate"
fi

# Verify activation worked
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "Error: Failed to activate virtual environment at '$VENV_PATH'"
    exit 1
fi

echo "Successfully activated virtual environment: $VIRTUAL_ENV"

echo "Updating pip..."
python -m pip install --upgrade pip

# -- Check for CUDA (assuming nvidia-smi for CUDA detection)
if command -v nvidia-smi &> /dev/null; then
    echo "CUDA detected. Installing GPU requirements..."
    pip install -r requirements/shared.txt -r requirements/gpu.txt -r requirements/standalone_interface.txt
else
    echo "CUDA not detected. Installing CPU requirements..."
    pip install -r requirements/shared.txt -r requirements/cpu.txt -r requirements/standalone_interface.txt
fi

# -- run installation for "unstructured[all-docs]"
pip install "unstructured[all-docs]"

# Install tesseract system dependency
if [[ "$PACKAGE_MANAGER" == "brew" ]]; then
    echo "Installing tesseract via Homebrew..."
    brew install tesseract
elif [[ "$PACKAGE_MANAGER" == "apt" ]]; then
    echo "Installing tesseract via APT..."
    sudo apt install tesseract-ocr -y
elif [[ "$IS_WINDOWS" == true ]]; then
    echo "Note: Please install tesseract manually on Windows or ensure it's already installed."
fi

# Install Python tesseract packages
pip install tesserocr
pip install pytesseract

# -- installing tesseract language datasets..
LANGUAGES="eng fra deu spa ita por kor ara"
USER_TESSDATA="$HOME/.local/share/tessdata"
mkdir -p "$USER_TESSDATA"

echo "Installing language files to $USER_TESSDATA"
for lang in $LANGUAGES; do
    echo "Downloading $lang.traineddata..."
    if command -v wget &>/dev/null; then
        wget -q -O "$USER_TESSDATA/$lang.traineddata" "https://github.com/tesseract-ocr/tessdata/raw/main/$lang.traineddata"
    elif command -v curl &>/dev/null; then
        curl -s -L -o "$USER_TESSDATA/$lang.traineddata" "https://github.com/tesseract-ocr/tessdata/raw/main/$lang.traineddata"
    else
        echo "Neither wget nor curl found. Please install one of them."
        exit 1
    fi
    
    [ -f "$USER_TESSDATA/$lang.traineddata" ] && echo "✓ $lang" || echo "✗ $lang failed"
done

export TESSDATA_PREFIX="$USER_TESSDATA"
RC_FILE="$HOME/.bashrc"
if [[ -f "$RC_FILE" ]]; then
    grep -q "TESSDATA_PREFIX" "$RC_FILE" || echo "export TESSDATA_PREFIX=\"$USER_TESSDATA\"" >> "$RC_FILE"
    echo "Environment variable set in $RC_FILE"
fi

echo "Installation complete. TESSDATA_PREFIX=$USER_TESSDATA"
echo "Installation complete..All packages installed into $VENV_PATH" 