absl-py==2.1.0
accelerate==0.33.0
aiofiles==24.1.0
aiohttp==3.9.5
aiosignal==1.3.1
altair==5.3.0
annotated-types==0.6.0
anyio==4.3.0
arrow==1.3.0
asgiref==3.8.1
asttokens==2.4.1
attrs==23.2.0
Authlib==1.3.1
backcall==0.2.0
backoff==2.2.1
bcrypt==4.1.3
beautifulsoup4==4.12.3
bert-score==0.3.13
bitsandbytes==0.43.3
bleach==6.1.0
blinker==1.8.2
build==1.2.1
cachetools==5.3.3
certifi==2024.2.2
cffi==1.16.0
chardet==5.2.0
charset-normalizer==3.3.2
chroma-hnswlib==0.7.3
chromadb==0.5.0
click==8.1.7
cloudpickle==3.0.0
cmake==3.30.2
codecarbon==2.8.0
coloredlogs==15.0.1
compressed-tensors-nightly==0.5.0.20240909
contourpy==1.3.0
cryptography==43.0.0
cycler==0.12.1
dataclasses-json==0.6.6
datasets==2.21.0
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.14
dill==0.3.8
diskcache==5.6.3
distlib==0.3.8
distro==1.9.0
dnspython==2.6.1
docopt==0.6.2
docstring_parser==0.16
einops==0.8.0
email_validator==2.1.1
emoji==2.14.0
eval_type_backport==0.2.0
executing==2.1.0
extra-streamlit-components==0.1.71
faiss-cpu==1.8.0
fastapi==0.111.0
fastapi-cli==0.0.4
fastjsonschema==2.20.0
fief-client==0.20.0
filelock==3.14.0
filetype==1.2.0
flash-attn==2.6.3
FlashRank==0.2.0
flatbuffers==24.3.25
fonttools==4.54.1
frozenlist==1.4.1
fsspec==2024.3.1
gekko==1.2.1
gitdb==4.0.11
GitPython==3.1.43
google-auth==2.29.0
googleapis-common-protos==1.63.0
GPUtil==1.4.0
greenlet==3.0.3
grpcio==1.65.4
grpcio-health-checking==1.65.4
grpcio-tools==1.65.4
h11==0.14.0
hf_transfer==0.1.8
html5lib==1.1
httpcore==1.0.5
httptools==0.6.1
httpx==0.27.0
huggingface-hub==0.24.5
humanfriendly==10.0
idna==3.7
importlib-metadata==7.0.0
importlib_resources==6.4.0
InstructorEmbedding==1.0.1
interegular==0.3.3
ipython==8.12.3
jedi==0.19.1
Jinja2==3.1.4
jiter==0.5.0
joblib==1.4.2
jsonpatch==1.33
jsonpath-python==1.0.6
jsonpointer==2.4
jsonschema==4.22.0
jsonschema-specifications==2023.12.1
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyterlab_pygments==0.3.0
jwcrypto==1.5.6
kiwisolver==1.4.7
kubernetes==29.0.0
langchain==0.1.20
langchain-community==0.0.38
langchain-core==0.1.52
langchain-text-splitters==0.0.1
langdetect==1.0.9
langsmith==0.1.57
language-tool-python==2.8
lark==1.2.2
llvmlite==0.43.0
lm-format-enforcer==0.10.3
loguru==0.7.2
lxml==5.3.0
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.21.2
matplotlib==3.9.2
matplotlib-inline==0.1.7
mdurl==0.1.2
mistune==3.0.2
mmh3==4.1.0
monotonic==1.6
mpmath==1.3.0
msgpack==1.0.8
multidict==6.0.5
multiprocess==0.70.16
mypy-extensions==1.0.0
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.3
ninja==********
nltk==3.9.1
numba==0.60.0
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-ml-py==12.555.43
nvidia-ml-py3==7.352.0
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.5.82
nvidia-nvtx-cu12==12.1.105
oauthlib==3.2.2
olefile==0.47
onnxruntime==1.18.0
openai==1.40.6
opentelemetry-api==1.24.0
opentelemetry-exporter-otlp-proto-common==1.24.0
opentelemetry-exporter-otlp-proto-grpc==1.24.0
opentelemetry-instrumentation==0.45b0
opentelemetry-instrumentation-asgi==0.45b0
opentelemetry-instrumentation-fastapi==0.45b0
opentelemetry-proto==1.24.0
opentelemetry-sdk==1.24.0
opentelemetry-semantic-conventions==0.45b0
opentelemetry-util-http==0.45b0
optimum==1.22.0
orjson==3.10.3
outlines==0.0.46
overrides==7.7.0
packaging==23.2
pandas==2.2.2
pandocfilters==1.5.1
parso==0.8.4
peft
pexpect==4.9.0
pickleshare==0.7.5
pillow==10.3.0
pipreqs==0.5.0
platformdirs==4.2.2
posthog==3.5.0
prometheus-fastapi-instrumentator==7.0.0
prometheus_client==0.20.0
prompt-toolkit==3.0.36
protobuf==3.20.3
psutil==5.9.8
ptyprocess==0.7.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
py3nvml==0.2.7
pyairports==2.1.1
pyarrow==16.0.0
pyasn1==0.6.0
pyasn1_modules==0.4.0
pycountry==24.6.1
pycparser==2.22
pydantic==2.9.2
pydantic_core==2.23.4
pydeck==0.9.1
Pygments==2.18.0
PyMuPDF==1.24.7
PyMuPDFb==1.24.6
pynvml==11.5.3
pyparsing==3.2.0
pypdf==4.2.0
PyPDF2==3.0.1
PyPika==0.48.9
pyproject_hooks==1.1.0
python-dateutil==2.8.2
python-dotenv==1.0.1
python-iso639==2024.10.22
python-magic==0.4.27
python-multipart==0.0.9
python-oxmsg==0.0.1
pytz==2024.1
PyYAML==6.0.1
pyzmq==26.1.0
questionary==2.0.1
rank-bm25==0.2
RapidFuzz==3.10.1
ray==2.34.0
referencing==0.35.1
regex==2024.5.10
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==13.7.1
rouge==1.0.1
rouge_score==0.1.2
rpds-py==0.18.1
rsa==4.9
safetensors==0.4.3
scikit-learn==1.4.2
scipy==1.13.0
seaborn==0.13.2
sentence-transformers==2.2.2
sentencepiece==0.2.0
setuptools==71.1.0
shellingham==1.5.4
shtab==1.7.1
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.30
stack-data==0.6.3
starlette==0.37.2
streamlit==1.34.0
sympy==1.12
tenacity==8.3.0
termcolor==2.3.0
threadpoolctl==3.5.0
tiktoken==0.7.0
tinycss2==1.4.0
tokenizers==0.19.1
toml==0.10.2
toolz==0.12.1
torch==2.4.0
torchvision==0.19.0
tornado==6.4
tqdm==4.66.4
traitlets==5.14.3
transformers==4.44.2
triton==3.0.0
trl==0.9.6
typer==0.12.3
types-python-dateutil==2.9.0.20241003
typing-inspect==0.9.0
typing_extensions==4.11.0
tyro==0.8.10
tzdata==2024.1
ujson==5.10.0
unsloth
unstructured==0.16.6
unstructured-client==0.28.0
urllib3==2.2.1
uvicorn==0.29.0
uvloop==0.19.0
validators==0.33.0
virtualenv==20.26.3
vllm==0.5.4
vllm-flash-attn==2.6.1
watchdog==4.0.1
watchfiles==0.21.0
wcwidth==0.2.13
weaviate-client==4.7.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==12.0
wheel==0.43.0
wrapt==1.16.0
xformers==0.0.27.post2
XlsxWriter==3.2.0
xmltodict==0.14.2
xxhash==3.4.1
yarg==0.1.9
yarl==1.9.4
yaspin==3.1.0
zipp==3.18.2
