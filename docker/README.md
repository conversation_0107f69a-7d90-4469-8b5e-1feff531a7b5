# Docker

The files here are responsible for creating a docker image and deploying it on our servers (using kubernetes) or locally (using docker).

## Build and run OmniRAG

For any local building / deployment, you need to [**install docker**](https://www.docker.com/products/docker-desktop/) on your machine, and launch it.

1. Build the image using `docker build . <your-image-tag>`
    - If you expect to run the image on a OVH instance, specify the destination platform using `--platform linux/amd64` (defaults to your machine)
    - If you expect to run the image on CPU, specify the destination type of device using `--build-arg DEVICE=cpu` (defaults to `gpu`)
2. Optional - Store your image in DockerHub:
    - Tag the image you just built using `docker tag <your-image-tag> <your-repo>:latest`
    - Push your image on your DockerHub repo using `docker push <your-repo>:latest`
3. Optional - Download your image on DockerHub using `docker pull <your-repo>:latest`
4. Configure your `.env` file. You can take `docker/example.env` as an example (default configuration).
4. Run your image using `docker run -p 8509:8509 --env-file <path-to-your-env-file> <your-image-tag>`
    - If you run the image on GPU, add `--gpus all` before your image tag.
    - If you want to mount a volume to share data (like VDBs) with your container, add `-v <data-folder-path> <destination-folder-path>` before you image tag.<br/>For example, if I want to provide my vector store folder located at `~/home/<USER>/vector_store/`, I will add `-v ~/home/<USER>/vector_store/ /app/vector_store/`
